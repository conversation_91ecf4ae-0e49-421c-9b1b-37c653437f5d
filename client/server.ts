import { AngularAppEngine } from '@angular/ssr';
import { VERSION } from '@angular/core';
import express from 'express';
import { fileURLToPath } from 'node:url';
import { dirname, resolve } from 'node:path';
import dotenv from 'dotenv'
dotenv.config()

console.log('Angular version:', VERSION.full);

// The Express app is exported so that it can be used by serverless Functions.
export function app(): express.Express {
  const server = express();
  const serverDistFolder = dirname(fileURLToPath(import.meta.url));
  const browserDistFolder = resolve(serverDistFolder, '../browser');

  const angularAppEngine = new AngularAppEngine();

  server.set('view engine', 'html');
  server.set('views', browserDistFolder);

  // Example Express Rest API endpoints
  // server.get('/api/**', (req, res) => { });
  // Serve static files from /browser
  server.get('**', express.static(browserDistFolder, {
    maxAge: '1y',
    index: 'index.html',
  }));

  // All regular routes use the Angular engine
  server.get('**', async (req, res, next) => {
    try {
      res.set('Cache-Control', 'no-store, no-cache, must-revalidate, private');

      const request = new Request(`${req.protocol}://${req.get('host')}${req.originalUrl}`, {
        method: req.method,
        headers: req.headers as Record<string, string>,
      });

      const response = await angularAppEngine.handle(request);

      if (response) {
        res.status(response.status);

        // Copy headers from the response
        response.headers.forEach((value, key) => {
          res.setHeader(key, value);
        });

        const body = await response.text();
        res.send(body);
      } else {
        next();
      }
    } catch (err: any) {
      next(err);
    }
  });

  return server;
}

function run(): void {
  const port = process.env['CLIENT_PORT'] ? parseInt(process.env['CLIENT_PORT']) : 4000;
  const host = process.env['HOST'] || 'localhost'
  // Start up the Node server
  const server = app();
  server.listen(port, host, () => {
    console.log(`Node Express server listening on http://${host}:${port}`);
  });
}

run();
